"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainModel = exports.ai = void 0;
const vertexai_1 = require("@genkit-ai/vertexai");
const genkit_1 = require("genkit");
const firebase_1 = require("@genkit-ai/firebase");
const v2_1 = require("firebase-functions/v2");
const mainModel = vertexai_1.gemini20Flash001;
exports.mainModel = mainModel;
// Configure Genkit instance with Vertex AI in European region
const ai = (0, genkit_1.genkit)({
    plugins: [
        (0, vertexai_1.vertexAI)({
            location: "europe-west1", // Frankfurt, Germany
        }),
    ],
    model: mainModel,
});
exports.ai = ai;
// Enable Firebase telemetry for better monitoring
(0, firebase_1.enableFirebaseTelemetry)();
v2_1.logger.info("Genkit AI initialized with Vertex AI in europe-west1");
//# sourceMappingURL=genkit-config.js.map