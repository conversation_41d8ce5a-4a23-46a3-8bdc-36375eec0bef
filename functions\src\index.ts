import * as v2 from "firebase-functions/v2";
import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v1";
import { FieldValue, Timestamp } from "firebase-admin/firestore";

import { FraudPreventionService } from "./services/fraudPrevention";
import {
  Auth,
  ChangeUserRoleData,
  CreateUserInvitationData,
  AcceptInvitationData,
  SyncUserRoleClaimsData,
  SwitchActiveOrganizationData,
  UpdateOrganizationData,
  CreateOrganizationData,
  DeleteOrganizationData
} from "./types";

import { functionConfig } from "./lib/function-config";
import { generateUserStoryFunction } from "./lib/ai/handlers/story-generator";

// Define and export the generateUserStory Cloud Function correctly
exports.generateUserStory = functions
  .region("europe-west1")
  .https.onCall(generateUserStoryFunction);

// Initialize the admin SDK (if not already done elsewhere)
admin.initializeApp();

const isEmu = !!process.env.FUNCTIONS_EMULATOR || !!process.env.FIRESTORE_EMULATOR_HOST || !!process.env.FIREBASE_STORAGE_EMULATOR_HOST;
export const serverTimestamp = () => isEmu ? new Date() : FieldValue.serverTimestamp();


// Initialize fraud prevention service
const fraudPreventionService = new FraudPreventionService();

/**
 * Cloud function triggered when a user is created
 * Sends a welcome email and schedules inactivity emails
 */
// export const onUserSignup = v2.firestore.onDocumentCreated(
//   {
//     document: "users/{userId}",
//     region: getFirebaseRegion(),
//   },
//   async (event) => {
//     const userId = event.params.userId;

//     v2.logger.info("Processing new user signup", { userId });

//     // Send welcome email immediately
//     try {
//       const userData = (await db.collection("users").doc(userId).get()).data();

//       if (!userData) {
//         v2.logger.error("User data not found", { userId });
//         throw new Error("User data not found");
//       }

//       await emailService.sendEmail(
//         {
//           userId,
//           type: "welcome",
//           scheduledFor: new Date(),
//           status: "pending",
//           attempts: 0,
//         },
//         userData
//       );

//       // Create task with 'sent' status for history
//       await db.collection("emailTasks").add({
//         userId,
//         type: "welcome",
//         scheduledFor: Timestamp.now(),
//         status: "sent",
//         attempts: 1,
//         sentAt: Timestamp.now(),
//       });

//       v2.logger.info("Welcome email sent successfully and task created", {
//         userId,
//       });
//     } catch (error) {
//       v2.logger.error("Failed to send welcome email", { userId, error });
//       // Create a task for retry in case of failure
//       await db.collection("emailTasks").add({
//         userId,
//         type: "welcome",
//         scheduledFor: Timestamp.now(),
//         status: "pending",
//         attempts: 0,
//       });
//     }

//     // Schedule inactivity email for 3 days later
//     const threeDaysLater = new Date();
//     threeDaysLater.setDate(threeDaysLater.getDate() + 3);

//     try {
//       await db.collection("emailTasks").add({
//         userId,
//         type: "inactive",
//         scheduledFor: Timestamp.fromDate(threeDaysLater),
//         status: "pending",
//         attempts: 0,
//       });
//       v2.logger.info("Inactivity email task created", {
//         userId,
//         scheduledFor: threeDaysLater,
//       });
//     } catch (error) {
//       v2.logger.error("Failed to create inactivity email task", {
//         userId,
//         error,
//       });
//       throw error;
//     }
//   }
// );

/**
 * Cloud function triggered when a user is deleted
 * Stores a pseudonymized token to prevent abuse without storing PII
 */
export const onUserDeleted = functions
  .region("europe-west1")
  .auth
  .user()
  .onDelete(async (user: admin.auth.UserRecord) => {
    try {
      if (!user || !user.email) {
        v2.logger.error("Missing user data for deletion token creation");
        return;
      }

      // Store token for fraud prevention using only email (GDPR-minimal approach)
      await fraudPreventionService.storeDeletedAccountToken(user.email);

      // Delete user profile from Firestore
      const userProfileRef = admin.firestore().collection('userProfiles').doc(user.uid);
      await userProfileRef.delete();

      v2.logger.info("Created pseudonymized token and deleted user profile for account", {
        userId: user.uid,
      });
    } catch (error) {
      v2.logger.error("Error during user deletion cleanup", { error });
    }
  });

/**
 * Before user creation callable function - validates if the current registration
 * appears to be an abuse/reuse of a deleted account.
 *
 * GDPR Compliant as we only store pseudonymized tokens without PII
 */
export const validateNewUser = v2.https.onCall(
  functionConfig,
  async (request) => {
    try {
      const { email } = request.data || {};
      v2.logger.info("Validating new user registration attempt");

      if (!email) {
        v2.logger.warn(
          "Registration validation failed: Missing email parameter",
        );
        throw new v2.https.HttpsError(
          "invalid-argument",
          "Email is required for validation",
        );
      }

      v2.logger.info("Checking for existing token", {
        emailHash: hashEmail(email),
      });
      // Check if a token exists for this email (simplified approach)
      const tokenExists = await fraudPreventionService.checkTokenExists(email);

      if (tokenExists) {
        // We found a matching token - this appears to be account recycling
        // We don't tell the user exactly why they can't register to prevent circumvention
        v2.logger.warn("Registration blocked: Token exists for email", {
          emailHash: hashEmail(email),
        });
        throw new v2.https.HttpsError(
          "permission-denied",
          "Registrierung nicht möglich. Bitte kontaktieren Sie den Support.",
        );
      }

      // No token found, user is allowed to create an account
      v2.logger.info("User validation successful, registration allowed", {
        emailHash: hashEmail(email),
      });
      return { allowed: true };
    } catch (error) {
      if (error instanceof v2.https.HttpsError) {
        throw error;
      }

      v2.logger.error("Error validating new user", { error });
      throw new v2.https.HttpsError(
        "internal",
        "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.",
      );
    }
  },
);

// Helper function to hash email for logging purposes
function hashEmail(email: string): string {
  // Simple hash function for logging - not for security purposes
  return Buffer.from(email).toString("base64").substring(0, 8);
}

/**
 * Scheduled function to remove expired tokens
 * Ensures we comply with GDPR's data minimization principle
 */
export const cleanupExpiredTokens = v2.scheduler.onSchedule(
  {
    schedule: "every 24 hours",
    region: "europe-west1",
  },
  async (event) => {
    try {
      const count = await fraudPreventionService.purgeExpiredTokens();
      v2.logger.info(`Cleaned up ${count} expired tokens`);
    } catch (error) {
      v2.logger.error("Error cleaning up expired tokens", { error });
    }
  },
);

// Custom Claims

// exports.setInitialClaims = functions
//   .region("europe-west1")
//   .https.onCall(
//     async (data: any, context: any) => {
//   // Sicherheitscheck: Funktion darf nur vom authentifizierten Benutzer aufgerufen werden
//       if (!context || !context.auth) {
//     throw new functions.https.HttpsError(
//       "unauthenticated",
//           "Nicht authentifiziert",
//         );
//       }

//       const auth = context.auth as Auth;
//       const typedData = data as SetInitialClaimsData;
//       const organizationId = typedData.organizationId;

//       if (!organizationId) {
//         throw new functions.https.HttpsError(
//           "invalid-argument",
//           "organizationId ist erforderlich",
//     );
//   }

//   // Nur dem eigenen Account dürfen Claims gesetzt werden (Sicherheit)
//       const uid = auth.uid;

//   try {
//     // Claims im Token setzen
//     const newClaims = {
//       activeRole: "admin", // Erste Benutzer ist immer Admin
//       activeOrganizationId: organizationId,
//       organizations: {
//         // Dynamischer Schlüssel für die Organisations-ID
//         [organizationId]: "admin"
//       }
//     };

//     await admin.auth().setCustomUserClaims(uid, newClaims);

//     // Optional: Force-Refresh des Token auslösen, damit Claims sofort wirksam werden
//     // Dies erfordert einen zusätzlichen Eintrag in der Datenbank, auf den der Client hören kann
//     await admin.firestore().collection("userMetadata").doc(uid).set({
//       refreshTime: serverTimestamp(),
//     });

//     return { success: true };
//   } catch (error) {
//         v2.logger.error("Fehler beim Setzen der Claims:", error);
//         throw new functions.https.HttpsError(
//           "internal",
//           "Fehler beim Setzen der Berechtigungen",
//         );
//       }
//     },
//   );

/**
 * Cloud Function zum Ändern der Rolle eines Benutzers
 * Nur Admins können Rollen von Benutzern in ihrer Organisation ändern
 */
exports.changeUserRole = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Sicherheitscheck: Nur authentifizierte Benutzer dürfen Rollen ändern
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Nicht authentifiziert",
        );
      }

      const auth = context.auth as Auth;
      const typedData = data as ChangeUserRoleData;

      // Prüfe, ob der Benutzer ein Admin ist
      if (!auth.token.activeRole || auth.token.activeRole !== "admin") {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Nur Administratoren dürfen Benutzerrollen ändern",
        );
      }

      const targetUid = typedData.targetUid;
      const newRole = typedData.newRole;

      // Validierung der Eingabeparameter
      if (!targetUid || !newRole) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Ziel-Benutzer-ID und neue Rolle müssen angegeben werden",
        );
      }

      // Zulässige Rollen prüfen
      const allowedRoles = ["admin", "manager", "viewer", "accountant"];
      if (!allowedRoles.includes(newRole)) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          `Ungültige Rolle: ${newRole}. Erlaubte Rollen: ${allowedRoles.join(", ")}`,
        );
      }

      // Verhindern, dass Benutzer ihre eigene Rolle ändern (Sicherheitsmaßnahme)
      if (targetUid === auth.uid) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Sie können Ihre eigene Rolle nicht ändern",
        );
      }

      const activeOrganizationId = auth.token.activeOrganizationId;
      if (!activeOrganizationId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Keine aktive Organisation gefunden",
        );
      }

      try {
        // 1. Prüfen, ob der Zielbenutzer existiert
        const targetUser = await admin.auth().getUser(targetUid);
        if (!targetUser) {
          throw new functions.https.HttpsError(
            "not-found",
            "Der angegebene Benutzer existiert nicht",
          );
        }

        // 2. Prüfen, ob der Zielbenutzer in der gleichen Organisation ist
        const userOrgsRef = admin
          .firestore()
          .collection("userOrganizations")
          .where("userId", "==", targetUid)
          .where("organizationId", "==", activeOrganizationId)
          .limit(1);

        const userOrgsSnapshot = await userOrgsRef.get();

        if (userOrgsSnapshot.empty) {
          throw new functions.https.HttpsError(
            "permission-denied",
            "Sie können nur Benutzer in Ihrer eigenen Organisation verwalten",
          );
        }

        const userOrgDoc = userOrgsSnapshot.docs[0];

        // 3. UserOrganization-Eintrag aktualisieren
        await userOrgDoc.ref.update({
          role: newRole,
          updatedAt: serverTimestamp(),
        });

        // 4. Claims des ZIELBENUTZERS IMMER aktualisieren, um die Rolle in der Map zu ändern
        const targetUserClaims = targetUser.customClaims || {};
        const targetOrgs = targetUserClaims.organizations || {};

        // Rolle im organizations-Objekt aktualisieren
        targetOrgs[activeOrganizationId] = newRole;

        // Vorbereitete neue Claims mit aktualisiertem organizations-Objekt
        const newClaims: {[key: string]: any} = {
          ...targetUserClaims, // Alle bestehenden Claims beibehalten
          organizations: targetOrgs, // Aktualisiertes org-Objekt
        };

        // Prüfen, ob die geänderte Organisation die AKTIVE des Zielbenutzers ist
        if (targetUserClaims.activeOrganizationId === activeOrganizationId) {
          // Nur dann auch die activeRole aktualisieren
          newClaims.activeRole = newRole;
        }

        // Aktualisierte Claims setzen
        await admin.auth().setCustomUserClaims(targetUid, newClaims);

        // 5. Token-Refresh IMMER auslösen, da Claims geändert wurden
        await admin.firestore().collection("userMetadata").doc(targetUid).set({
          refreshTime: serverTimestamp(),
        });

        v2.logger.info(
          `Rolle von Benutzer ${targetUid} wurde auf ${newRole} geändert in Org ${activeOrganizationId}`,
        );
        return { success: true, message: "Rolle erfolgreich geändert" };
      } catch (error) {
        v2.logger.error("Fehler beim Ändern der Benutzerrolle:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error; // Bereits vorhandene HttpsError weiterwerfen
        }

        throw new functions.https.HttpsError(
          "internal",
          "Ein interner Fehler ist beim Ändern der Rolle aufgetreten",
        );
      }
    },
  );

/**
 * Cloud Function zum Einladen eines neuen Benutzers in eine Organisation
 * Erstellt einen Einladungslink, den der Admin an den Benutzer senden kann
 */
exports.createUserInvitation = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Sicherheitscheck: Nur authentifizierte Benutzer können Einladungen erstellen
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Nicht authentifiziert",
        );
      }

      const auth = context.auth as Auth;
      const typedData = data as CreateUserInvitationData;

      // Prüfe, ob der Benutzer ein Admin oder Manager ist
      const userRole = auth.token.activeRole;
      const hasPermission = userRole === "admin" || userRole === "manager";

      if (!hasPermission) {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Nur Administratoren und Manager können Benutzer einladen",
        );
      }

      const email = typedData.email;
      const role = typedData.role || "viewer";

      // Validierung der Eingabeparameter
      if (!email) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "E-Mail-Adresse muss angegeben werden",
        );
      }

      // Zulässige Rollen prüfen
      const allowedRoles = ["admin", "manager", "viewer", "accountant"];
      if (!allowedRoles.includes(role)) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          `Ungültige Rolle: ${role}. Erlaubte Rollen: ${allowedRoles.join(", ")}`,
        );
      }

      // Manager dürfen keine Admins erstellen
      if (userRole === "manager" && role === "admin") {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Manager können keine Administrator-Konten erstellen",
        );
      }

      const activeOrganizationId = auth.token.activeOrganizationId;

      if (!activeOrganizationId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Keine aktive Organisation gefunden",
        );
      }

      try {
        // 1. Organisation abrufen
        const orgDoc = await admin
          .firestore()
          .collection("organizations")
          .doc(activeOrganizationId)
          .get();

        if (!orgDoc.exists) {
          throw new functions.https.HttpsError(
            "not-found",
            "Organisation nicht gefunden",
          );
        }

        // 2. Einladung erstellen
        const invitationData = {
          email: email.toLowerCase(),
          role,
          organizationId: activeOrganizationId,
          organizationName: orgDoc.data()?.name || "Ihre Organisation",
          invitedBy: auth.uid,
          invitedByEmail: auth.token.email || "",
          status: "pending",
          createdAt: serverTimestamp(),
          expiresAt: Timestamp.fromDate(
            new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 Tage gültig
          ),
        };

        const invitationRef = await admin
          .firestore()
          .collection("invitations")
          .add(invitationData);

        // 3. Generiere den Einladungslink
        // In einer echten Anwendung würde hier vielleicht eine E-Mail gesendet
        const invitationId = invitationRef.id;
        const invitationLink = `${process.env.FRONTEND_URL || "https://vermietos.de"}/invitation?id=${invitationId}`;

        return {
          success: true,
          invitationId,
          invitationLink,
        };
      } catch (error) {
        v2.logger.error("Fehler beim Erstellen der Einladung:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "Ein interner Fehler ist beim Erstellen der Einladung aufgetreten",
        );
      }
    },
  );

/**
 * Cloud Function zum Akzeptieren einer Einladung und Beitreten zu einer Organisation
 */
exports.acceptInvitation = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Sicherheitscheck: Nur authentifizierte Benutzer können Einladungen annehmen
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Nicht authentifiziert",
        );
      }

      const auth = context.auth as Auth;
      const typedData = data as AcceptInvitationData;
      const invitationId = typedData.invitationId;

      // Validierung der Eingabeparameter
      if (!invitationId) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Einladungs-ID muss angegeben werden",
        );
      }

      try {
        // 1. Einladung abrufen
        const invitationDoc = await admin
          .firestore()
          .collection("invitations")
          .doc(invitationId)
          .get();

        if (!invitationDoc.exists) {
          throw new functions.https.HttpsError(
            "not-found",
            "Einladung nicht gefunden",
          );
        }

        const invitation = invitationDoc.data();

        // 2. Prüfen, ob die Einladung abgelaufen ist
        if (invitation?.expiresAt && invitation.expiresAt.toDate() < new Date()) {
          throw new functions.https.HttpsError(
            "failed-precondition",
            "Diese Einladung ist abgelaufen",
          );
        }

        // 3. Prüfen, ob die Einladung bereits angenommen wurde
        if (invitation?.status !== "pending") {
          throw new functions.https.HttpsError(
            "failed-precondition",
            "Diese Einladung wurde bereits verwendet oder storniert",
          );
        }

        // 4. Prüfen, ob die E-Mail-Adresse des angemeldeten Benutzers mit der Einladung übereinstimmt
        // Optional: Diese Prüfung kann je nach Anforderungen weggelassen werden
        if (invitation?.email && invitation.email !== auth.token.email) {
          throw new functions.https.HttpsError(
            "permission-denied",
            "Diese Einladung ist für eine andere E-Mail-Adresse bestimmt",
          );
        }

        // 5. Benutzerprofil erstellen oder aktualisieren
        const userProfileRef = admin
          .firestore()
          .collection("userProfiles")
          .doc(auth.uid);

        const userProfileSnap = await userProfileRef.get();
        const now = serverTimestamp();

        // Prüfen, ob Benutzer bereits in dieser Organisation ist
        const userOrgQuery = await admin
          .firestore()
          .collection("userOrganizations")
          .where("userId", "==", auth.uid)
          .where("organizationId", "==", invitation.organizationId)
          .limit(1)
          .get();

        if (!userOrgQuery.empty) {
          throw new functions.https.HttpsError(
            "failed-precondition",
            "Sie sind bereits Mitglied dieser Organisation",
          );
        }

        if (!userProfileSnap.exists) {
          // Neues Benutzerprofil erstellen
          const user = await admin.auth().getUser(auth.uid);
          const displayName = user.displayName || "";
          const nameParts = displayName.split(" ");
          const firstName = nameParts[0] || "";
          const lastName = nameParts.slice(1).join(" ") || "";

          await userProfileRef.set({
            email: user.email,
            displayName: displayName,
            firstName: firstName,
            lastName: lastName,
            createdAt: now,
            lastLogin: now,
          });
        } else {
           // Update lastLogin timestamp if profile exists
           await userProfileRef.update({ lastLogin: now });
        }

        // 6. UserOrganization-Eintrag erstellen
        const userOrgRef = admin
          .firestore()
          .collection("userOrganizations")
          .doc();
        await userOrgRef.set({
          userId: auth.uid,
          organizationId: invitation.organizationId,
          role: invitation.role,
          createdAt: now,
          lastAccessed: now,
        });

        // 7. Custom Claims im Auth-Token aktualisieren
        const user = await admin.auth().getUser(auth.uid);
        const currentClaims = user.customClaims || {};
        const currentOrgs = currentClaims.organizations || {};

        // Neue Organisation hinzufügen
        currentOrgs[invitation.organizationId] = invitation.role;

        // Neue Claims setzen (bestehende beibehalten, neue Org hinzufügen, aktive Org/Rolle setzen)
        const newClaims = {
          ...currentClaims, // Alle bestehenden Claims beibehalten
          organizations: currentOrgs, // Aktualisiertes org-Objekt
          activeOrganizationId: invitation.organizationId, // Neue Org als aktiv setzen
          activeRole: invitation.role, // Rolle der neuen Org als aktiv setzen
        };

        await admin.auth().setCustomUserClaims(auth.uid, newClaims);

        // 8. Token-Refresh auslösen
        await admin.firestore().collection("userMetadata").doc(auth.uid).set({
          refreshTime: now,
        });

        // 9. Einladung als angenommen markieren
        await invitationDoc.ref.update({
          status: "accepted",
          acceptedBy: auth.uid,
          acceptedAt: now,
        });

        return {
          success: true,
          organizationId: invitation.organizationId,
          role: invitation.role,
        };
      } catch (error) {
        v2.logger.error("Fehler beim Annehmen der Einladung:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "Ein interner Fehler ist beim Annehmen der Einladung aufgetreten",
        );
      }
    },
  );

/**
 * Cloud Function zum Synchronisieren der Benutzerrollen mit den Auth Custom Claims
 * Diese Funktion kann bei Bedarf manuell aufgerufen werden
 */
exports.syncUserRoleClaims = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Sicherheitscheck: Nur authentifizierte Benutzer dürfen die Funktion aufrufen
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Nicht authentifiziert"
        );
      }

      const auth = context.auth as Auth;
      const typedData = data as SyncUserRoleClaimsData;

      let userId = typedData.userId || auth.uid;
      const organizationId = typedData.organizationId || auth.token.activeOrganizationId;

      // Wenn keine Organisations-ID angegeben ist, werfen wir einen Fehler
      if (!organizationId) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Eine Organisations-ID muss angegeben werden"
        );
      }

      // Wenn ein Admin einen anderen Benutzer synchronisieren möchte
      if (userId !== auth.uid) {
        const isAdmin = auth.token.activeRole === "admin";
        if (!isAdmin) {
          throw new functions.https.HttpsError(
            "permission-denied",
            "Nur Administratoren können Claims anderer Benutzer synchronisieren"
          );
        }

        // Sicherstellen, dass der Zielbenutzer und Admin in der gleichen Organisation sind
        if (organizationId !== auth.token.activeOrganizationId) {
          throw new functions.https.HttpsError(
            "permission-denied",
            "Sie können nur Benutzer in Ihrer aktiven Organisation verwalten"
          );
        }
      }

      try {
        // 1. Prüfen, ob der Benutzer Zugang zur angegebenen Organisation hat
        const userOrgsRef = admin
          .firestore()
          .collection("userOrganizations")
          .where("userId", "==", userId)
          .where("organizationId", "==", organizationId);

        const userOrgsSnapshot = await userOrgsRef.get();

        if (userOrgsSnapshot.empty) {
          throw new functions.https.HttpsError(
            "not-found",
            "Der Benutzer hat keinen Zugang zu dieser Organisation"
          );
        }

        // 2. Die Rolle des Benutzers in dieser Organisation auslesen
        const userOrgData = userOrgsSnapshot.docs[0].data();
        const roleInOrg = userOrgData.role || "viewer";

        // 3. Custom Claims aktualisieren
        const user = await admin.auth().getUser(userId);
        const currentClaims = user.customClaims || {};
        const currentOrgs = currentClaims.organizations || {};

        // Stelle sicher, dass die Rolle für die Ziel-Org im Objekt aktuell ist
        if (currentOrgs[organizationId] !== roleInOrg) {
          currentOrgs[organizationId] = roleInOrg;
        }

        // Neue Claims setzen (bestehende beibehalten, aktive Org/Rolle setzen, Org-Objekt aktualisieren)
        const newClaims = {
          ...currentClaims,
          organizations: currentOrgs, // Sicherstellen, dass das Objekt aktuell ist
          activeOrganizationId: organizationId, // Ziel-Org als aktiv setzen
          activeRole: roleInOrg, // Gelesene Rolle als aktiv setzen
        };

        await admin.auth().setCustomUserClaims(userId, newClaims);

        // 4. Last accessed Timestamp aktualisieren
        await userOrgsSnapshot.docs[0].ref.update({
          lastAccessed: serverTimestamp()
        });

        // 5. Token-Refresh auslösen
        await admin
          .firestore()
          .collection("userMetadata")
          .doc(userId)
          .set({
            refreshTime: serverTimestamp()
          });

        return {
          success: true,
          message: "Benutzerrolle erfolgreich synchronisiert",
          organizationId,
          role: roleInOrg
        };
      } catch (error) {
        v2.logger.error("Fehler beim Synchronisieren der Benutzerrolle:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "Ein interner Fehler ist beim Synchronisieren der Benutzerrolle aufgetreten"
        );
      }
    },
  );

/**
 * Cloud Function to switch a user's active organization
 * Updates the user's custom claims to set the active organization and role
 */
exports.switchActiveOrganization = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Security check: Only authenticated users can switch organizations
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Authentication required",
        );
      }

      const auth = context.auth as Auth;
      const typedData = data as SwitchActiveOrganizationData;
      const organizationId = typedData.organizationId;
      const userId = auth.uid;

      // Validate input parameters
      if (!organizationId) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Organization ID must be provided",
        );
      }

      try {
        // 1. Check if the user has access to this organization
        const userOrgsRef = admin
          .firestore()
          .collection("userOrganizations")
          .where("userId", "==", userId)
          .where("organizationId", "==", organizationId);

        const userOrgsSnapshot = await userOrgsRef.get();

        if (userOrgsSnapshot.empty) {
          throw new functions.https.HttpsError(
            "permission-denied",
            "You do not have access to this organization",
          );
        }

        // Get the user's role in that organization
        const userOrgData = userOrgsSnapshot.docs[0].data();
        const roleInOrg = userOrgData.role || "viewer";

        // 2. Get the organization details
        const orgRef = admin
          .firestore()
          .collection("organizations")
          .doc(organizationId);
        const orgDoc = await orgRef.get();

        if (!orgDoc.exists) {
          throw new functions.https.HttpsError(
            "not-found",
            "Organization not found",
          );
        }

        // 3. Update custom claims to set active organization and role
        const user = await admin.auth().getUser(userId);
        const currentClaims = user.customClaims || {};
        const currentOrgs = currentClaims.organizations || {};

        // Stelle sicher, dass die Rolle für die Ziel-Org im Objekt aktuell ist
        if (currentOrgs[organizationId] !== roleInOrg) {
          currentOrgs[organizationId] = roleInOrg;
        }

        // Keep existing claims but update active organization and role
        const newClaims = {
          ...currentClaims,
          organizations: currentOrgs, // Sicherstellen, dass das Objekt aktuell ist
          activeOrganizationId: organizationId, // Ziel-Org als aktiv setzen
          activeRole: roleInOrg, // Gelesene Rolle als aktiv setzen
        };

        await admin.auth().setCustomUserClaims(userId, newClaims);

        // 4. Update the user's last accessed timestamp for this organization
        await userOrgsSnapshot.docs[0].ref.update({
          lastAccessed: serverTimestamp(),
        });

        // 5. Trigger token refresh
        await admin.firestore().collection("userMetadata").doc(userId).set({
          refreshTime: serverTimestamp(),
        });

        return {
          success: true,
          message: "Organization switched successfully",
          organizationId,
          role: roleInOrg,
        };
      } catch (error) {
        v2.logger.error("Error switching organization:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "An internal error occurred while switching organization",
        );
      }
    },
  );

/**
 * Cloud Function to create a new organization
 * Creates organization document and sets up the user as an admin
 */
exports.createOrganization = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Security check: Only authenticated users can create organizations
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Authentication required",
        );
      }

      const auth = context.auth as Auth;
      const userId = auth.uid;

      // Extract and validate input data
      const typedData = data as CreateOrganizationData;
      const { name, contactEmail, address, iban, accountHolder, bic, bankName } = typedData;

      if (!name || typeof name !== "string" || name.trim().length === 0) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Organization name is required",
        );
      }

      try {
        // 1. Create a new organization document with a generated ID
        const orgRef = admin.firestore().collection("organizations").doc();
        const organizationId = orgRef.id;

        const timestamp = serverTimestamp();

        // Create the organization data object
        const organizationData: {
          name: string;
          contactEmail: string | null;
          iban?: string;
          accountHolder?: string;
          bic?: string;
          bankName?: string;
          createdAt: any;
          updatedAt: any;
          createdByUserId: string;
          address?: {[key: string]: any};
        } = {
          name: name.trim(),
          contactEmail: contactEmail || null,
          createdAt: timestamp,
          updatedAt: timestamp,
          createdByUserId: userId,
        };

        // Add payment details if provided
        if (iban) organizationData.iban = iban;
        if (accountHolder) organizationData.accountHolder = accountHolder;
        if (bic) organizationData.bic = bic;
        if (bankName) organizationData.bankName = bankName;

        // Add address if provided
        if (address) {
          // Filter out null/empty address fields
          const cleanAddress: {[key: string]: any} = {};
          Object.keys(address).forEach(key => {
            if (address[key as keyof typeof address]) {
              cleanAddress[key] = address[key as keyof typeof address];
            }
          });

          if (Object.keys(cleanAddress).length > 0) {
            organizationData["address"] = cleanAddress;
          }
        }

        // 2. Create the organization document
        await orgRef.set(organizationData);

        // 3. Add user as admin of this organization
        const userOrgRef = admin.firestore().collection("userOrganizations").doc();
        await userOrgRef.set({
          userId: userId,
          organizationId: organizationId,
          role: "admin", // Always admin for the creator
          createdAt: timestamp,
          lastAccessed: timestamp
        });

        // 4. Update user's custom claims to make this the active organization
        const user = await admin.auth().getUser(userId);
        const currentClaims = user.customClaims || {};
        const currentOrgs = currentClaims.organizations || {};

        // Neue Organisation zum Objekt hinzufügen
        currentOrgs[organizationId] = "admin"; // Ersteller ist Admin

        await admin.auth().setCustomUserClaims(userId, {
          ...currentClaims,
          organizations: currentOrgs, // Aktualisiertes Objekt hinzufügen
          activeOrganizationId: organizationId, // Neue Org als aktiv setzen
          activeRole: "admin" // Immer Admin für den Ersteller
        });

        // 5. Trigger token refresh
        await admin.firestore().collection("userMetadata").doc(userId).set({
          refreshTime: timestamp
        });

        // 4. Log completion
        v2.logger.info(`Registration completed for user ${userId} with organization ${organizationId}`);

        // 5. NOW return the result
        return {
          success: true,
          organizationId: organizationId
        };
      } catch (error) {
        v2.logger.error("Error creating new organization:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "An internal error occurred while creating the organization",
        );
      }
    },
  );

/**
 * Cloud Function to update organization details
 * Allows admins to update organization name and contact email
 */
exports.updateOrganization = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Security check: Only authenticated users can update organization details
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Authentication required",
        );
      }

      const auth = context.auth as Auth;
      const typedData = data as UpdateOrganizationData;
      const { name, contactEmail, iban, accountHolder, bic, bankName } = typedData;

      // Validate input parameters
      if (!name && !contactEmail && !iban && !accountHolder && !bic && !bankName) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "At least one field must be provided to update",
        );
      }

      // Get the active organization ID from the user's custom claims
      const activeOrganizationId = auth.token.activeOrganizationId;
      const userRole = auth.token.activeRole;

      // Check if user is admin
      if (userRole !== "admin") {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Only administrators can update organization details",
        );
      }

      if (!activeOrganizationId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "No active organization found",
        );
      }

      try {
        // Get the organization document reference
        const orgRef = admin
          .firestore()
          .collection("organizations")
          .doc(activeOrganizationId);

        // Check if organization exists
        const orgDoc = await orgRef.get();
        if (!orgDoc.exists) {
          throw new functions.https.HttpsError(
            "not-found",
            "Organization not found",
          );
        }

        // Update only the fields that are provided
        const updateData: { [key: string]: any } = {
          updatedAt: serverTimestamp(),
        };

        if (name) {
          updateData.name = name;
        }

        if (contactEmail) {
          updateData.contactEmail = contactEmail;
        }

        // Add payment details if provided
        if (iban) {
          updateData.iban = iban;
        }

        if (accountHolder) {
          updateData.accountHolder = accountHolder;
        }

        if (bic) {
          updateData.bic = bic;
        }

        if (bankName) {
          updateData.bankName = bankName;
        }

        // Update the organization document
        await orgRef.update(updateData);

        return {
          success: true,
          message: "Organization details updated successfully",
        };
      } catch (error) {
        v2.logger.error("Error updating organization details:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "An internal error occurred while updating organization details",
        );
      }
    },
  );



/**
 * Cloud Function to handle the complete user registration process
 * Creates organization, user profile, and userOrganization entry
 */
exports.createUserAndOrganization = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Security check: Only authenticated users can create their profile and organization
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Authentication required"
        );
      }

      const auth = context.auth as Auth;
      const userId = auth.uid;
      const email = auth.token.email;

      if (!email) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "User must have an email address"
        );
      }

      // Extract registration data
      const {
        organizationName,
        firstName,
        lastName,
        displayName = null,
        githubToken = null // Extract GitHub token from request
      } = data;

      // Validate required fields
      if (!organizationName || !organizationName.trim()) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Organization name is required"
        );
      }

      try {
        let organizationId: string;
        
        // Use a transaction to ensure all or nothing is created
        const transactionResult = await admin.firestore().runTransaction(async (transaction) => {
          // Check if user profile already exists (prevent duplicate registration)
          const userProfileRef = admin.firestore().collection("userProfiles").doc(userId);
          const userProfileDoc = await transaction.get(userProfileRef);

          if (userProfileDoc.exists) {
            throw new functions.https.HttpsError(
              "already-exists",
              "User profile already exists"
            );
          }

          // 1. Create new organization
          const orgRef = admin.firestore().collection("organizations").doc();
          organizationId = orgRef.id;

          transaction.set(orgRef, {
            name: organizationName.trim(),
            contactEmail: email,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            createdByUserId: userId
          });

          // 2. Create user profile with provided information
          const resolvedDisplayName = displayName ||
            `${(firstName || '').trim()} ${(lastName || '').trim()}`.trim();

          const userProfileData: {[key: string]: any} = {
            email: email,
            displayName: resolvedDisplayName,
            firstName: firstName || '',
            lastName: lastName || '',
            createdAt: serverTimestamp(),
            lastLogin: serverTimestamp()
          };
          
          // Add GitHub token if available
          if (githubToken) {
            userProfileData.githubToken = githubToken;
          }

          transaction.set(userProfileRef, userProfileData);

          // 3. Create userOrganization entry
          const userOrgRef = admin.firestore().collection("userOrganizations").doc();
          transaction.set(userOrgRef, {
            userId: userId,
            organizationId: organizationId,
            role: "admin", // First user is always admin
            createdAt: serverTimestamp(),
            lastAccessed: serverTimestamp()
          });

          return {
            success: true,
            organizationId: organizationId,
            displayName: resolvedDisplayName
          };
        });
        
        // IMPORTANT: Ensure organizationId is accessible here
        organizationId = transactionResult.organizationId;
        v2.logger.info(`Transaction completed. Now setting claims for user ${userId} with organization ${organizationId}`);
        
        // Set custom claims immediately after the transaction
        const newClaims = {
          activeRole: "admin",
          activeOrganizationId: organizationId,
          organizations: {
            [organizationId]: "admin"
          }
        };

        // 1. Set custom claims - this is critical
        await admin.auth().setCustomUserClaims(userId, newClaims);
        v2.logger.info(`Custom claims set for user ${userId}`, { claims: JSON.stringify(newClaims) });

        // 2. Trigger token refresh
        await admin.firestore().collection("userMetadata").doc(userId).set({
          refreshTime: serverTimestamp()
        });
        v2.logger.info(`Token refresh triggered for user ${userId}`);

        // 4. Log completion
        v2.logger.info(`Registration completed for user ${userId} with organization ${organizationId}`);

        // 5. Return the result
        return {
          success: true,
          organizationId: organizationId
        };
      } catch (error) {
        v2.logger.error("Error during user registration:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "An internal error occurred during registration"
        );
      }
    },
  );

/**
 * Cloud Function to delete an organization and all its related data
 * This is a destructive operation that cannot be undone
 */
exports.deleteOrganization = functions
  .region("europe-west1")
  .https.onCall(
    async (data: any, context: any) => {
      // Security check: Only authenticated users with admin role can delete organizations
      if (!context || !context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Nicht authentifiziert",
        );
      }

      const auth = context.auth as Auth;
      const typedData = data as DeleteOrganizationData;

      // Check if user is an admin
      const userRole = auth.token.activeRole;
      if (userRole !== "admin") {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Nur Administratoren können Organisationen löschen",
        );
      }

      const organizationId = typedData.organizationId;
      const activeOrgId = auth.token.activeOrganizationId;
      const userId = auth.uid;

      // Validate parameters
      if (!organizationId) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Organizations-ID muss angegeben werden",
        );
      }

      // Check if requested org matches active org
      if (organizationId !== activeOrgId) {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Sie können nur die aktive Organisation löschen",
        );
      }

      try {
        const db = admin.firestore();
        const storage = admin.storage();
        const bucket = storage.bucket();

        // First check if user has access to other organizations
        const userOrgsRef = db
          .collection("userOrganizations")
          .where("userId", "==", userId);

        const userOrgsSnapshot = await userOrgsRef.get();

        // Count organizations user is in and identify alternatives
        let orgCount = 0;
        let alternativeOrgs: {id: string, role: string}[] = [];

        userOrgsSnapshot.forEach(doc => {
          const data = doc.data();
          if (data.organizationId !== organizationId) {
            alternativeOrgs.push({
              id: data.organizationId,
              role: data.role
            });
          }
          orgCount++;
        });

        // Prevent deletion if this is the user's only organization
        if (orgCount <= 1) {
          throw new functions.https.HttpsError(
            "failed-precondition",
            "Diese Organisation kann nicht gelöscht werden, da es Ihre einzige Organisation ist. Erstellen Sie zuerst eine neue Organisation."
          );
        }

        // 1. Get organization document to verify it exists
        const orgDoc = await db
          .collection("organizations")
          .doc(organizationId)
          .get();

        if (!orgDoc.exists) {
          throw new functions.https.HttpsError(
            "not-found",
            "Organisation nicht gefunden",
          );
        }

        v2.logger.info(`Starting deletion process for organization: ${organizationId}`);

        // 2. Delete user-organization relationships
        const userOrgsQuery = await db
          .collection("userOrganizations")
          .where("organizationId", "==", organizationId)
          .get();

        await deleteQueryBatch(db, userOrgsQuery);
        v2.logger.info(`Deleted ${userOrgsQuery.size} user organization relationships`);

        // 3. Delete pending invitations
        const invitationsQuery = await db
          .collection("invitations")
          .where("organizationId", "==", organizationId)
          .get();

        await deleteQueryBatch(db, invitationsQuery);
        v2.logger.info(`Deleted ${invitationsQuery.size} pending invitations`);

        // 4. Delete folders subcollection
        try {
          const foldersRef = db.collection("organizations").doc(organizationId).collection("folder");
          const foldersSnapshot = await foldersRef.get();

          if (!foldersSnapshot.empty) {
            await deleteQueryBatch(db, foldersSnapshot);
            v2.logger.info(`Deleted ${foldersSnapshot.size} folders`);
          }
        } catch (error) {
          v2.logger.error(`Error deleting folders: ${error}`);
          // Continue with deletion even if this part fails
        }

        // 5. Delete documents subcollection
        try {
          const documentsRef = db.collection("organizations").doc(organizationId).collection("documents");
          const documentsSnapshot = await documentsRef.get();

          // Delete document files from storage if they have storage references
          const storageDeletePromises = [];
          for (const doc of documentsSnapshot.docs) {
            const data = doc.data();
            if (data.storagePath) {
              storageDeletePromises.push(bucket.file(data.storagePath).delete().catch(
                err => v2.logger.warn(`Failed to delete file ${data.storagePath}: ${err}`)
              ));
            }
          }

          // Wait for all storage deletions to complete
          await Promise.all(storageDeletePromises);

          // Then delete document metadata
          if (!documentsSnapshot.empty) {
            await deleteQueryBatch(db, documentsSnapshot);
            v2.logger.info(`Deleted ${documentsSnapshot.size} documents`);
          }
        } catch (error) {
          v2.logger.error(`Error deleting documents: ${error}`);
          // Continue with deletion even if this part fails
        }

        // 6. Delete objects and their units (more complex due to nested structure)
        try {
          // First get all objects
          const objectsRef = db.collection("organizations").doc(organizationId).collection("objects");
          const objectsSnapshot = await objectsRef.get();

          // For each object, delete its units subcollection first
          const objectIds = objectsSnapshot.docs.map(doc => doc.id);

          for (const objectId of objectIds) {
            const unitsRef = db
              .collection("organizations")
              .doc(organizationId)
              .collection("objects")
              .doc(objectId)
              .collection("units");

            const unitsSnapshot = await unitsRef.get();

            if (!unitsSnapshot.empty) {
              await deleteQueryBatch(db, unitsSnapshot);
              v2.logger.info(`Deleted ${unitsSnapshot.size} units for object ${objectId}`);
            }
          }

          // Then delete the objects themselves
          if (!objectsSnapshot.empty) {
            await deleteQueryBatch(db, objectsSnapshot);
            v2.logger.info(`Deleted ${objectsSnapshot.size} objects`);
          }
        } catch (error) {
          v2.logger.error(`Error deleting objects and units: ${error}`);
          // Continue with deletion even if this part fails
        }

        // 7. Delete all storage files in organization folder
        try {
          // Correct storage path is /organizations/{orgId}
          const [files] = await bucket.getFiles({ prefix: `organizations/${organizationId}` });

          if (files.length > 0) {
            const fileDeletePromises = files.map(file =>
              file.delete().catch(err => v2.logger.warn(`Failed to delete file ${file.name}: ${err}`))
            );

            await Promise.all(fileDeletePromises);
            v2.logger.info(`Deleted ${files.length} storage files`);
          }
        } catch (storageError) {
          v2.logger.error("Error deleting storage files:", storageError);
          // Continue with deletion even if storage cleanup fails
        }

        // 8. Finally, delete the organization document itself
        await db.collection("organizations").doc(organizationId).delete();
        v2.logger.info(`Successfully deleted organization ${organizationId} and all related data`);

        // Update user claims to reflect changes and set a new active organization
        const userClaims = await admin.auth().getUser(auth.uid).then(user => user.customClaims || {});

        // Remove the deleted organization from claims
        if (userClaims.organizations && userClaims.organizations[organizationId]) {
          delete userClaims.organizations[organizationId];

          // Set a new active organization if available
          if (alternativeOrgs.length > 0) {
            // Take the first available organization as the new active one
            const newActiveOrg = alternativeOrgs[0];
            userClaims.activeOrganizationId = newActiveOrg.id;
            userClaims.activeRole = newActiveOrg.role;

            v2.logger.info(`Set new active organization ${newActiveOrg.id} for user ${userId}`);
          } else {
            // No other organizations available
            delete userClaims.activeOrganizationId;
            delete userClaims.activeRole;
          }

          // Update the custom claims
          await admin.auth().setCustomUserClaims(auth.uid, userClaims);

          // Force token refresh
          await db.collection("userMetadata").doc(auth.uid).set({
            refreshTime: serverTimestamp(),
          });
        }

        return {
          success: true,
          message: "Organisation und alle zugehörigen Daten wurden erfolgreich gelöscht.",
          newActiveOrganizationId: alternativeOrgs.length > 0 ? alternativeOrgs[0].id : null
        };
      } catch (error) {
        v2.logger.error("Fehler beim Löschen der Organisation:", error);

        if (error instanceof functions.https.HttpsError) {
          throw error;
        }

        throw new functions.https.HttpsError(
          "internal",
          "Ein interner Fehler ist beim Löschen der Organisation aufgetreten"
        );
      }
    }
  );

/**
 * Helper function to delete documents in batches (Firestore has a limit of 500 operations per batch)
 */
async function deleteQueryBatch(db: FirebaseFirestore.Firestore, query: FirebaseFirestore.QuerySnapshot) {
  if (query.size === 0) {
    return;
  }

  const batchSize = 400; // Firestore limit for batch operations
  let batch = db.batch();
  let count = 0;

  for (const doc of query.docs) {
    batch.delete(doc.ref);
    count++;

    // Commit when batch reaches limit
    if (count >= batchSize) {
      await batch.commit();
      batch = db.batch();
      count = 0;
    }
  }

  // Commit any remaining documents
  if (count > 0) {
    await batch.commit();
  }
}

