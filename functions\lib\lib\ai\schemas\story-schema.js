"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserStorySchema = void 0;
const genkit_1 = require("genkit");
// Schema for structured user story output
exports.UserStorySchema = genkit_1.z.object({
    title: genkit_1.z.string(),
    role: genkit_1.z.string(),
    goal: genkit_1.z.string(),
    benefit: genkit_1.z.string(),
    formattedStory: genkit_1.z.string(),
    // New evaluation fields
    feasibility: genkit_1.z.number().min(1).max(5),
    complexity: genkit_1.z.number().min(1).max(10),
    priority: genkit_1.z.enum(["Critical", "High", "Medium", "Low"]),
    featureCategory: genkit_1.z.string(),
});
//# sourceMappingURL=story-schema.js.map