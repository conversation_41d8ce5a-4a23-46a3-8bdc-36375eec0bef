/**
 * Creates a prompt to transform feedback into a structured user story
 */
export function createStoryPrompt(feedbackText: string, projectContext: string): string {
  return `
    Transformiere das folgende Nutzerfeedback in eine gut strukturierte User Story auf Deutsch:
    
    FEEDBACK:
    "${feedbackText}"
    
    PROJEKT-KONTEXT:
    "${projectContext}"
    
    Anweisungen:
    1. Analysiere das Feedback und extrahiere den wichtigsten Nutzerbedarf oder die Funktionsanfrage
    2. Erstelle eine User Story im Format "Als [ROLLE] möchte ich [ZIEL], damit [NUTZEN]"
    3. <PERSON><PERSON><PERSON> einen prägnanten, beschreibenden Titel für die Story vor
    4. Falls das Feedback unklar ist oder keine gültige Funktionsanfrage enthält, erstelle die wahrscheinlichste User Story basierend auf dem Kontext
    5. Konzentriere dich auf den wichtigsten Punkt, wenn das Feedback mehrere Anfragen enthält
    6. <PERSON><PERSON>hre eine umfassende Bewertung der Story anhand der unten beschriebenen Kriterien durch
    
    Bewertungskriterien:
    1. Machbarkeit (Feasibility): Bewerte auf einer Skala von 1-5, wie technisch machbar die Implementierung ist. Identifiziere spezifische technische Einschränkungen oder Herausforderungen.
    2. Sinnhaftigkeit im Projektkontext (Project Context Alignment): Beurteile, ob und wie die Funktion mit den Gesamtprojektzielen und dem vorhandenen Funktionsumfang übereinstimmt. Nenne spezifische Gründe, warum sie im aktuellen Kontext sinnvoll ist oder nicht. Begründe dies in maximal einem Satz.
    3. Komplexität (Complexity): Bewerte die Implementierungskomplexität auf einer Skala von 1-10.
    4. Priorität (Priority): Weise eine Prioritätsstufe (Critical, High, Medium, Low) basierend auf Benutzereinfluss und Geschäftswert zu.
    5. Feature-Kategorisierung (Feature Categorization): Identifiziere, zu welcher bestehenden Funktionskategorie oder Epic diese Anfrage gehört, oder schlage eine neue Kategorie vor, wenn sie nicht zu den bestehenden passt.
    
    Gib das Ergebnis als JSON-Objekt mit folgenden Feldern aus:
    - title: Ein prägnanter, beschreibender Titel (max. 10 Wörter)
    - role: Die Benutzerrolle (wer diese Funktion möchte)
    - goal: Was sie erreichen möchten (die Aktion/Funktion)
    - benefit: Warum sie es möchten (der Wert/Nutzen)
    - formattedStory: Die vollständige User Story formatiert als "Als [role] möchte ich [goal], damit [benefit]"
    - feasibility: Machbarkeitsbewertung (1-5)
    - complexity: Komplexitätsbewertung (1-10)
    - priority: Prioritätsstufe (Critical, High, Medium, Low)
    - featureCategory: Die zugehörige Feature-Kategorie oder Epic
  `;
} 