'use client';

import { useEffect, useState } from 'react';
import { db } from '@/app/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { useAuth } from '@/components/providers/auth-provider';
import { toast } from 'sonner';
import { Loader2, GitBranch } from 'lucide-react';

interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body: string | null;
  state: 'open' | 'closed';
  labels: Array<{
    name: string;
    color: string;
  }>;
  created_at: string;
  updated_at: string;
  pull_request?: any; // GitHub API includes this field for PRs
}

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
}

interface IssuesFetcherProps {
  selectedRepo: Repository;
  onIssuesContent: (content: string) => void;
}

export function IssuesFetcher({ selectedRepo, onIssuesContent }: IssuesFetcherProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [issuesStats, setIssuesStats] = useState<{ open: number; closed: number } | null>(null);

  useEffect(() => {
    async function fetchIssues() {
      if (!user || !selectedRepo) {
        setIssuesStats(null);
        setError(null);
        onIssuesContent('');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the GitHub token from Firestore
        const userProfileRef = doc(db, "userProfiles", user.uid);
        const userProfileSnap = await getDoc(userProfileRef);

        if (!userProfileSnap.exists() || !userProfileSnap.data().githubToken) {
          setError("Kein GitHub-Token gefunden. Bitte melden Sie sich erneut an.");
          return;
        }

        const githubToken = userProfileSnap.data().githubToken;

        // Fetch both open and closed issues
        const [openResponse, closedResponse] = await Promise.all([
          fetch(`https://api.github.com/repos/${selectedRepo.full_name}/issues?state=open&per_page=100`, {
            headers: {
              'Authorization': `token ${githubToken}`,
              'Accept': 'application/vnd.github.v3+json'
            }
          }),
          fetch(`https://api.github.com/repos/${selectedRepo.full_name}/issues?state=closed&per_page=100`, {
            headers: {
              'Authorization': `token ${githubToken}`,
              'Accept': 'application/vnd.github.v3+json'
            }
          })
        ]);

        if (!openResponse.ok || !closedResponse.ok) {
          throw new Error(`GitHub API error: ${openResponse.status} / ${closedResponse.status}`);
        }

        const [openIssues, closedIssues]: [GitHubIssue[], GitHubIssue[]] = await Promise.all([
          openResponse.json(),
          closedResponse.json()
        ]);

        // Filter out pull requests (GitHub API returns both issues and PRs)
        const filteredOpenIssues = openIssues.filter(issue => !issue.pull_request);
        const filteredClosedIssues = closedIssues.filter(issue => !issue.pull_request);

        const allIssues = [...filteredOpenIssues, ...filteredClosedIssues];

        // Create formatted context for the AI
        const issuesContext = formatIssuesForContext(filteredOpenIssues, filteredClosedIssues);

        setIssuesStats({
          open: filteredOpenIssues.length,
          closed: filteredClosedIssues.length
        });

        onIssuesContent(issuesContext);

      } catch (error) {
        console.error('Error fetching GitHub issues:', error);
        setError('Fehler beim Laden der GitHub Issues. Bitte versuchen Sie es erneut.');
        setIssuesStats(null);
        onIssuesContent('');
      } finally {
        setIsLoading(false);
      }
    }

    fetchIssues();
  }, [user, selectedRepo, onIssuesContent]);

  // Format issues for AI context
  function formatIssuesForContext(openIssues: GitHubIssue[], closedIssues: GitHubIssue[]): string {
    let context = '';

    if (openIssues.length > 0) {
      context += `\n\nOFFENE GITHUB ISSUES (${openIssues.length}):\n`;
      openIssues.forEach(issue => {
        const labels = issue.labels.map(label => label.name).join(', ');
        context += `- #${issue.number}: ${issue.title}`;
        if (labels) context += ` [${labels}]`;
        if (issue.body && issue.body.length > 0) {
          // Include first 200 characters of issue body for context
          const bodyPreview = issue.body.substring(0, 200).replace(/\n/g, ' ');
          context += ` - ${bodyPreview}${issue.body.length > 200 ? '...' : ''}`;
        }
        context += '\n';
      });
    }

    if (closedIssues.length > 0) {
      context += `\n\nGESCHLOSSENE GITHUB ISSUES (${closedIssues.length}):\n`;
      closedIssues.forEach(issue => {
        const labels = issue.labels.map(label => label.name).join(', ');
        context += `- #${issue.number}: ${issue.title}`;
        if (labels) context += ` [${labels}]`;
        if (issue.body && issue.body.length > 0) {
          // Include first 150 characters for closed issues (slightly less detail)
          const bodyPreview = issue.body.substring(0, 150).replace(/\n/g, ' ');
          context += ` - ${bodyPreview}${issue.body.length > 150 ? '...' : ''}`;
        }
        context += '\n';
      });
    }

    return context;
  }

  if (error) {
    return (
      <div className="mt-4">
        <div className="flex items-center space-x-2 mb-2">
          <GitBranch className="h-4 w-4 text-red-500" />
          <span className="text-sm font-medium text-red-600">Fehler beim Laden der Issues</span>
        </div>
        <p className="text-xs text-red-500">{error}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="mt-4">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">Lade GitHub Issues...</span>
        </div>
      </div>
    );
  }

  if (issuesStats) {
    return (
      <div className="mt-4">
        <div className="flex items-center space-x-2 mb-2">
          <GitBranch className="h-4 w-4" />
          <span className="text-sm font-medium">GitHub Issues als Kontext geladen</span>
        </div>
        <div className="p-3 bg-muted rounded-md">
          <p className="text-xs text-muted-foreground">
            {issuesStats.open} offene, {issuesStats.closed} geschlossene Issues geladen
          </p>
        </div>
      </div>
    );
  }

  return null;
}
