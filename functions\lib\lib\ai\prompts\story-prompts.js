"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createStoryPrompt = void 0;
/**
 * Creates a prompt to transform feedback into a structured user story
 */
function createStoryPrompt(feedbackText, projectContext, issuesContext = '') {
    return `
    Transformiere das folgende Nutzerfeedback in eine gut strukturierte User Story auf Deutsch:

    FEEDBACK:
    "${feedbackText}"

    PROJEKT-KONTEXT:
    "${projectContext}"

    GITHUB ISSUES KONTEXT:
    "${issuesContext}"

    Anweisungen:
    1. Analysiere das Feedback und extrahiere den wichtigsten Nutzerbedarf oder die Funktionsanfrage
    2. Erstelle eine User Story im Format "Als [ROLLE] möchte ich [ZIEL], damit [NUTZEN]"
    3. Schlage einen prägnanten, beschreibenden Titel für die Story vor
    4. Falls das Feedback unklar ist oder keine gültige Funktionsanfrage enthält, erstelle die wahrscheinlichste User Story basierend auf dem Kontext
    5. Konzentriere dich auf den wichtigsten Punkt, wenn das Feedback mehrere Anfragen enthält
    6. Führe eine umfassende Bewertung der Story anhand der unten beschriebenen Kriterien durch
    7. **WICHTIG**: Prüfe die GitHub Issues auf ähnliche oder doppelte Funktionalität:
       - Vergleiche mit offenen Issues (geplante Arbeit)
       - Vergleiche mit geschlossenen Issues (bereits implementierte Features)
       - Bewerte, ob die neue User Story bestehende Arbeit ergänzt oder damit in Konflikt steht
       - Berücksichtige dies in deiner Machbarkeits- und Prioritätsbewertung

    Bewertungskriterien:
    1. Machbarkeit (Feasibility): Bewerte auf einer Skala von 1-5, wie technisch machbar die Implementierung ist. Berücksichtige dabei auch ähnliche Issues und deren Status.
    2. Sinnhaftigkeit im Projektkontext (Project Context Alignment): Beurteile, ob und wie die Funktion mit den Gesamtprojektzielen, dem vorhandenen Funktionsumfang UND den GitHub Issues übereinstimmt. Prüfe auf Duplikate oder Konflikte.
    3. Komplexität (Complexity): Bewerte die Implementierungskomplexität auf einer Skala von 1-10. Berücksichtige dabei verwandte Issues und deren Komplexität.
    4. Priorität (Priority): Weise eine Prioritätsstufe (Critical, High, Medium, Low) basierend auf Benutzereinfluss, Geschäftswert UND Relation zu bestehenden Issues zu.
    5. Feature-Kategorisierung (Feature Categorization): Identifiziere, zu welcher bestehenden Funktionskategorie oder Epic diese Anfrage gehört, basierend auf Issues und Projektstruktur.
    6. **Issue-Analyse**: Wenn ähnliche Issues gefunden wurden, erwähne diese kurz in der Bewertung und erkläre die Beziehung.

    Gib das Ergebnis als JSON-Objekt mit folgenden Feldern aus:
    - title: Ein prägnanter, beschreibender Titel (max. 10 Wörter)
    - role: Die Benutzerrolle (wer diese Funktion möchte)
    - goal: Was sie erreichen möchten (die Aktion/Funktion)
    - benefit: Warum sie es möchten (der Wert/Nutzen)
    - formattedStory: Die vollständige User Story formatiert als "Als [role] möchte ich [goal], damit [benefit]"
    - feasibility: Machbarkeitsbewertung (1-5)
    - complexity: Komplexitätsbewertung (1-10)
    - priority: Prioritätsstufe (Critical, High, Medium, Low)
    - featureCategory: Die zugehörige Feature-Kategorie oder Epic
  `;
}
exports.createStoryPrompt = createStoryPrompt;
//# sourceMappingURL=story-prompts.js.map