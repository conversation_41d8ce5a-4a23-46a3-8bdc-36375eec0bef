'use client';

import { useState } from 'react';
import { functions } from '@/app/firebase';
import { httpsCallable } from 'firebase/functions';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, FileText, Github } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/components/providers/auth-provider';
import { RepoSelector } from './github/repo-selector';
import { ReadmeFetcher } from './github/readme-fetcher';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

// Type definition for the user story response
interface UserStory {
  title: string;
  role: string;
  goal: string;
  benefit: string;
  formattedStory: string;
  feasibility: number;
  complexity: number;
  priority: "Critical" | "High" | "Medium" | "Low";
  featureCategory: string;
}

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
}

export default function StoryGenerator() {
  const { user } = useAuth();
  
  const [feedbackText, setFeedbackText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userStory, setUserStory] = useState<UserStory | null>(null);
  
  // GitHub integration
  const [selectedRepo, setSelectedRepo] = useState<Repository | null>(null);
  const [readmeContent, setReadmeContent] = useState('');

  const handleRepoSelect = (repo: Repository) => {
    setSelectedRepo(repo);
  };

  const handleReadmeContent = (content: string) => {
    setReadmeContent(content);
  };

  const generateUserStory = async () => {
    if (!feedbackText.trim()) {
      toast.error('Bitte geben Sie einen Feedback-Text ein, um eine User Story zu generieren.');
      return;
    }

    setIsLoading(true);
    setUserStory(null);

    try {
      // Prepare context data
      const finalContext = selectedRepo 
        ? `Repository: ${selectedRepo?.full_name}\n${readmeContent}` 
        : '';

      // Call the Firebase Cloud Function
      const generateStoryFunction = httpsCallable<
        { feedbackText: string; projectContext: string },
        UserStory
      >(functions, 'generateUserStory');

      const result = await generateStoryFunction({
        feedbackText,
        projectContext: finalContext,
      });

      setUserStory(result.data);
      
      toast.success('Ihre User Story wurde erfolgreich erstellt.');
    } catch (error) {
      console.error('Error generating user story:', error);
      toast.error('Fehler beim Generieren der User Story. Bitte versuchen Sie es erneut.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      <Card className="w-full shadow-sm h-fit col-span-1">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-2 text-2xl">
            <FileText className="h-6 w-6" />
            Feedback zu User Story
          </CardTitle>
          <CardDescription className="text-base mt-1">
            Wandeln Sie rohes Nutzerfeedback in strukturierte User Stories um
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="text-sm font-medium mb-2 block">Feedback-Text</label>
            <Textarea
              placeholder="Fügen Sie hier Nutzerfeedback oder Beobachtungen ein..."
              className="min-h-40 text-base resize-y"
              value={feedbackText}
              onChange={(e) => setFeedbackText(e.target.value)}
              disabled={isLoading}
            />
          </div>
          
          {user && (
            <div className="space-y-4 pt-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Github className="h-4 w-4" />
                  <h3 className="font-medium">GitHub Repository</h3>
                </div>
                <div className="w-1/2">
                  <RepoSelector
                    onRepoSelect={handleRepoSelect}
                    selectedRepo={selectedRepo}
                  />
                </div>
              </div>
              
              {selectedRepo && (
                <ReadmeFetcher
                  selectedRepo={selectedRepo}
                  onReadmeContent={handleReadmeContent}
                />
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="pt-2 pb-6">
          <Button 
            onClick={generateUserStory} 
            disabled={isLoading}
            size="lg"
            className="text-base px-6"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Generiere...
              </>
            ) : (
              'User Story generieren'
            )}
          </Button>
        </CardFooter>
      </Card>

      {userStory ? (
        <Card className="w-full shadow-sm overflow-hidden col-span-2">
          <CardHeader className="pb-4 border-b">
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-2xl font-bold">{userStory.title}</CardTitle>
                <CardDescription className="mt-1 text-base">
                  Generierte User Story
                </CardDescription>
              </div>
              <Badge className={
                userStory.priority === 'Critical' ? 'bg-red-100 text-red-800 hover:bg-red-200' :
                userStory.priority === 'High' ? 'bg-orange-100 text-orange-800 hover:bg-orange-200' :
                userStory.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' :
                'bg-green-100 text-green-800 hover:bg-green-200'
              }>
                {userStory.priority}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="py-6 space-y-6">
            <div className="p-5 rounded-lg bg-muted/40 border">
              <p className="text-base whitespace-pre-line leading-relaxed">{userStory.formattedStory}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-4 rounded-lg border bg-background">
                <h4 className="text-base font-medium mb-2">Rolle</h4>
                <p>{userStory.role}</p>
              </div>
              <div className="p-4 rounded-lg border bg-background">
                <h4 className="text-base font-medium mb-2">Ziel</h4>
                <p>{userStory.goal}</p>
              </div>
              <div className="p-4 rounded-lg border bg-background">
                <h4 className="text-base font-medium mb-2">Nutzen</h4>
                <p>{userStory.benefit}</p>
              </div>
            </div>
            
            <Separator className="my-2" />
            
            {/* Evaluation section */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Story-Bewertung</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Feasibility */}
                <div className="rounded-lg border p-5">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="text-base font-semibold">Machbarkeit</h4>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <div
                          key={i}
                          className={`w-8 h-8 flex items-center justify-center rounded-md ${
                            i < userStory.feasibility ? 'bg-primary text-primary-foreground' : 'bg-muted'
                          }`}
                        >
                          {i + 1}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                
                {/* Complexity */}
                <div className="rounded-lg border p-5">
                  <div className="flex justify-between items-center">
                    <h4 className="text-base font-semibold">Komplexität</h4>
                    <span className="px-3 py-1.5 text-sm rounded-full bg-muted font-medium">
                      {userStory.complexity}/10
                    </span>
                  </div>
                </div>
                
                {/* Feature Category */}
                <div className="rounded-lg border p-5">
                  <h4 className="text-base font-semibold mb-2">Feature-Kategorie</h4>
                  <p className="text-base">{userStory.featureCategory}</p>
                </div>
              </div>
              
              {/* Show which context was used */}
              {selectedRepo && readmeContent && (
                <div className="rounded-lg border p-5 mt-6 bg-muted/20">
                  <div className="flex items-center gap-2">
                    <Github className="h-5 w-5" />
                    <h4 className="text-base font-semibold">Repository-Kontext verwendet</h4>
                  </div>
                  <p className="mt-2">
                    Die Bewertung basiert auf dem Kontext aus dem Repository <strong>{selectedRepo.full_name}</strong>.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="py-5 border-t flex justify-end space-x-3">
            <Button variant="outline" size="lg" onClick={() => {
              const fullText = `
${userStory.title}

${userStory.formattedStory}

Bewertung:
- Machbarkeit: ${userStory.feasibility}/5
- Komplexität: ${userStory.complexity}/10
- Priorität: ${userStory.priority}
- Feature-Kategorie: ${userStory.featureCategory}
              `.trim();
              
              navigator.clipboard.writeText(fullText);
              toast.success("User Story mit Bewertung in die Zwischenablage kopiert");
            }}>
              In Zwischenablage kopieren
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="flex items-center justify-center h-full">
          <Card className="w-full shadow-sm p-8 text-center border-dashed border-2">
            <div className="flex flex-col items-center justify-center space-y-4 text-muted-foreground">
              <FileText className="h-12 w-12" />
              <p className="text-lg">Generieren Sie eine User Story, um das Ergebnis hier zu sehen</p>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
} 