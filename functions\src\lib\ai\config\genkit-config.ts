import { vertexA<PERSON>, gemini20Flash001 } from "@genkit-ai/vertexai";
import { genkit } from "genkit";
import { enableFirebaseTelemetry } from "@genkit-ai/firebase";
import { logger } from "firebase-functions/v2";

const mainModel = gemini20Flash001;
// Configure Genkit instance with Vertex AI in European region
const ai = genkit({
  plugins: [
    vertexAI({
      location: "europe-west1", // Frankfurt, Germany
    }),
  ],
  model: mainModel,
});

// Enable Firebase telemetry for better monitoring
enableFirebaseTelemetry();

logger.info("Genkit AI initialized with Vertex AI in europe-west1");

// Export the configured AI instance for use across the application
export { ai, mainModel };
