import * as functions from "firebase-functions/v1";
import { logger } from "firebase-functions/v2";
import { ai, mainModel } from "../config/genkit-config";
import { UserStorySchema, UserStory } from "../schemas/story-schema";
import { createStoryPrompt } from "../prompts/story-prompts";

/**
 * Transforms user feedback into a structured user story
 */
// Instead of exporting the function directly, create it to be exported from index.ts
const generateUserStoryFunction = async (data: any, context: any) => {
  const requestId = Date.now().toString();

  try {
    // Authentication is automatically handled by onCall
    if (!context || !context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required"
      );
    }

    const userId = context.auth.uid;

    // Basic validation
    if (!data.feedbackText || typeof data.feedbackText !== "string") {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Feedback text is required"
      );
    }

    const projectContext = data.projectContext || "";
    const issuesContext = data.issuesContext || "";

    logger.info("Generating user story from feedback", {
      requestId,
      userId,
      hasProjectContext: !!projectContext,
      hasIssuesContext: !!issuesContext
    });

    const prompt = createStoryPrompt(data.feedbackText, projectContext, issuesContext);

    const { output } = await ai.generate({
      model: mainModel,
      prompt,
      output: { schema: UserStorySchema },
      config: {
        temperature: 0.7,
        maxOutputTokens: 1024,
      },
    });

    logger.info("User story generated successfully", {
      requestId,
      userId,
      storyTitle: (output as UserStory).title,
    });

    return output;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    logger.error("Error generating user story", {
      requestId,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    // onCall automatically handles error forwarding to the client
    if (error instanceof functions.https.HttpsError) {
      throw error;
    } else {
      throw new functions.https.HttpsError("internal", errorMessage);
    }
  }
};

// Export the handler function to be used in index.ts
export { generateUserStoryFunction };
